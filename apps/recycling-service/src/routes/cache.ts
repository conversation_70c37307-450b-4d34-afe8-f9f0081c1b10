import { controller } from '@regionalmedienaustria/microservice-utils'
import { Router } from 'express'

import { CacheController } from '@/controllers/cacheController.js'

const cacheRouter = Router()

cacheRouter.get('/clear/collection/:id', controller.wrapController(CacheController.clearCollectionCache))

cacheRouter.get('/clear/active-collections', controller.wrapController(CacheController.clearActiveCollectionsCache))

cacheRouter.get('/clear-all', controller.wrapController(CacheController.clearAllCache))

// Get cache statistics (useful for debugging)
cacheRouter.get('/stats', controller.wrapController(CacheController.getCacheStats))

export { cacheRouter }