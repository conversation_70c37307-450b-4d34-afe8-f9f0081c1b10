import { constants, logger, RmaError, requestUtil } from '@regionalmedienaustria/microservice-utils'


import { DirectusService } from '../services/directusService.js'
import { RecyclingService } from '../services/recyclingService.js'
import { ScheduleService } from '../services/scheduleService.js'
// Add import for circuit breakers for monitoring
import { peiqApiCircuitBreaker, directusCircuitBreaker } from '../utils/circuitBreaker.js'

import type { Request, Response } from 'express'

export class RecyclingController {
  /**
   * Test endpoint for searching top articles
   * GET /v1/recycling/test/search
   * 
   * @Security Grant('peiq_api')
   * Query params:
   * - keywords: comma-separated keywords for search (required)
   * - grouping: 'district' or 'category' or 'category_per_district' (required)
   * - categoryIds: comma-separated category IDs (required)
   * - locationIds: comma-separated location IDs (trees_id from region) (required) 
   * - dateFrom: ISO date string (optional)
   * - dateTo: ISO date string (optional)
   * - sortBy: Sort field (optional)
   * - sortDir: Sort direction (optional)
   * - maxPerGroup: Max articles per group (optional)
   * - maxTotal: Max total articles (optional)
   * 
   * @example
   * GET /v1/recycling/test/search?keywords=Sport,Fußball&grouping=district&categoryIds=1,2,3&locationIds=117,118
   */
  public static async testSearch(req: Request, res: Response) {
    try {
      // Required parameters
      const keywords = requestUtil.getParameter(req, 'keywords', undefined)
      const grouping = requestUtil.getParameter(req, 'grouping', undefined)
      const categoryIds = requestUtil.getParameter(req, 'categoryIds', undefined)
      const locationIds = requestUtil.getParameter(req, 'locationIds', undefined)
      
      // Optional parameters for advanced search
      const dateFrom = requestUtil.getParameter(req, 'dateFrom', undefined)
      const dateTo = requestUtil.getParameter(req, 'dateTo', undefined)
      const sortBy = requestUtil.getParameter(req, 'sortBy', undefined)
      const sortDir = requestUtil.getParameter(req, 'sortDir', undefined)
      const maxPerGroup = requestUtil.getParameter(req, 'maxPerGroup', undefined)
      const maxTotal = requestUtil.getParameter(req, 'maxTotal', undefined)

      const parsedKeywords = keywords.split(',').map((k: string) => k.trim())
      const parsedCategoryIds = categoryIds.split(',').map((id: string) => id.trim())
      const parsedLocationIds = locationIds.split(',').map((id: string) => id.trim())

      if (!keywords || !grouping || !categoryIds || !locationIds) {
        const missingParams = []
        if (!keywords) {missingParams.push('keywords')}
        if (!grouping) {missingParams.push('grouping')}
        if (!categoryIds) {missingParams.push('categoryIds')}
        if (!locationIds) {missingParams.push('locationIds')}
        
        const missingParamError = new Error(`Missing required parameters: ${missingParams.join(', ')}`)
        logger.error(missingParamError)
        
        res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(
          new RmaError({
            error: missingParamError,
            statusCode: constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST,
          }),
        )
        return
      }

      logger.info('Testing top articles search', { keywords, grouping, categoryIds, locationIds })

      // Add validation for grouping parameter
      if (!['district', 'category', 'category_per_district'].includes(grouping)) {
        const invalidParamError = new Error('Grouping must be: district, category or category_per_district')
        logger.error(invalidParamError)
        
        res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(
          new RmaError({
            error: invalidParamError,
            statusCode: constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST,
          }),
        )
        return
      }
      

      const articles = await RecyclingService.testTopArticlesSearch({
        keywords: parsedKeywords,
        grouping: grouping as 'district' | 'category' | 'category_per_district',
        categoryIds: parsedCategoryIds,
        locationIds: parsedLocationIds,
        dateFrom,
        dateTo,
        sortBy,
        sortDir,
        maxPerGroup: maxPerGroup ? parseInt(maxPerGroup) : undefined,
        maxTotal: maxTotal ? parseInt(maxTotal) : undefined
      })

      res.status(constants.STATUS_CODE.SUCESS.OK).send({
        message: 'Top articles search completed successfully',
        success: true,
        data: {
          articlesFound: articles.length,
          articles,
          searchParams: {
            keywords: parsedKeywords,
            grouping,
            categoryIds: parsedCategoryIds,
            locationIds: parsedLocationIds,
            dateFrom,
            dateTo,
            sortBy,
            sortDir,
            maxPerGroup,
            maxTotal
          },
        },
      })
    } catch (error) {
      logger.error('Failed to test top articles search', { error })
      
      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error instanceof Error ? error : new Error(String(error)),
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
        }),
      )
    }
  }

  /**
   * Test endpoint for creating top articles collection in DRAFT mode
   * POST /v1/recycling/test/create
   * 
   * @Security Grant('peiq_api')
   * Body params:
   * - title: Article title (required)
   * - content: Article content (required)
   * - categoryId: Category ID (required)
   * - locationId: Location ID (trees_id from region) (required)
   * - userId: User ID for article creation (required)
   * - tags: Array of tags (optional)
   * 
   * @example
   * POST /v1/recycling/test/create
   * {
   *   "title": "Top Artikel der Woche",
   *   "content": "Die besten Artikel aus Wien",
   *   "categoryId": 1,
   *   "locationId": 117,
   *   "userId": 1234,
   *   "tags": ["test", "top-artikel"]
   * }
   */
  public static async testCreate(req: Request, res: Response) {
    try {
      const { title, content, categoryId, locationId, userId } = req.body

      if (!title || !content || !categoryId || !locationId || !userId) {
        const missingFields = []
        if (!title) {missingFields.push('title')}
        if (!content) {missingFields.push('content')}
        if (!categoryId) {missingFields.push('categoryId')}
        if (!locationId) {missingFields.push('locationId')}
        if (!userId) {missingFields.push('userId')}
        
        const missingFieldError = new Error(`Missing required fields: ${missingFields.join(', ')}`)
        logger.error(missingFieldError)
        
        res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(
          new RmaError({
            error: missingFieldError,
            statusCode: constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST,
          }),
        )
        return
      }

      logger.info('Testing top articles collection creation', { title, categoryId, locationId, userId })

      const newCollectionId = await RecyclingService.testTopArticlesCreation({
        title,
        content,
        categoryId: parseInt(categoryId, 10),
        locationId: parseInt(locationId, 10),
        userId: parseInt(userId, 10),
        tags: req.body.tags,
      })

      res.status(constants.STATUS_CODE.SUCESS.OK).send({
        message: 'Top articles collection created successfully in DRAFT mode',
        success: true,
        data: {
          newCollectionId,
          testData: {
            title,
            categoryId,
            locationId,
            userId,
            tags: req.body.tags,
          },
        },
      })
    } catch (error) {
      logger.error('Failed to test top articles collection creation', { error })
      
      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error instanceof Error ? error : new Error(String(error)),
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
        }),
      )
    }
  }

  /**
   * Manual trigger for specific collection (DIRECTUS FLOW BUTTON)
   * POST /v1/recycling/execute/:collectionId
   * 
   * This endpoint is designed to be triggered from Directus Flow buttons.
   * Internal admin operation like cache clearing.
   * 
   * Use Case: Admin clicks "Execute Now" button in Directus Collection interface
   * Security: None - internal system call from Directus CMS
   */
  public static async executeCollection(req: Request, res: Response) {
    const collectionId = requestUtil.getParameter(req, 'collectionId', undefined)

    if (!collectionId) {
      const missingParamError = new Error('Collection ID is required')
      logger.error(missingParamError)
      
      res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(
        new RmaError({
          error: missingParamError,
          statusCode: constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST,
        }),
      )
      return
    }

    try {
      logger.info('Manual execution triggered for collection', { collectionId })
      
      const result = await RecyclingService.executeManualCollection(collectionId)

      res.status(constants.STATUS_CODE.SUCESS.OK).send({
        message: 'Collection executed successfully',
        success: true,
        data: result,
      })
    } catch (error) {
      logger.error('Failed to execute collection', { error, collectionId })
      
      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error instanceof Error ? error : new Error(String(error)),
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
        }),
      )
    }
  }

  /**
   * Execute all scheduled collections
   * POST /v1/recycling/execute/scheduled
   */
  public static async executeScheduled(req: Request, res: Response) {
    try {
      logger.info('Manual execution of all scheduled collections triggered')
      
      const results = await RecyclingService.executeScheduledCollections()

      // Analyze errors if any occurred
      const failedCount = results.filter(r => r.collectionsFailed > 0).length
      let errorSummary = undefined
      
      if (failedCount > 0) {
        const { ErrorAggregator } = await import('../helpers/errorAggregator.js')
        errorSummary = ErrorAggregator.createErrorSummary(results)
        
        // Log the error summary
        logger.warn('Collection execution completed with errors', {
          errorSummary: ErrorAggregator.formatErrorSummary(errorSummary)
        })
      }

      res.status(constants.STATUS_CODE.SUCESS.OK).send({
        message: 'Scheduled top articles collections completed',
        success: true,
        data: {
          collectionsProcessed: results.length,
          collectionsSucceeded: results.filter(r => r.collectionsCreated > 0).length,
          collectionsFailed: failedCount,
          results,
          errorSummary,
          // Add circuit breaker status for monitoring
          circuitBreakerStatus: {
            peiqApi: peiqApiCircuitBreaker.getStatus(),
            directus: directusCircuitBreaker.getStatus()
          }
        },
      })
    } catch (error) {
      logger.error('Failed to execute scheduled collections', { error })
      
      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error instanceof Error ? error : new Error(String(error)),
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
        }),
      )
    }
  }

  /**
   * Get status of all collections
   * GET /v1/recycling/collections/status
   */
  public static async getCollectionsStatus(req: Request, res: Response) {
    try {
      const bypassCache = requestUtil.getParameter(req, 'bypassCache', false)
      const collections = await DirectusService.getActiveCollections({ bypassCache })
      const currentTime = new Date()

      const collectionsWithStatus = collections.map(collection => {
        const validation = ScheduleService.validateSchedule(collection, currentTime)
        return {
          id: collection.id,
          status: collection.status,
          title: collection.title,
          group: collection.group,
          nextScheduledTime: validation.nextScheduledTime,
          shouldRun: validation.shouldRun,
          reason: validation.reason,
          keywords: collection.keywords,
          grouping: collection.grouping,
          interval: collection.interval,
          days: collection.days,
          monthly_occurrence: collection.monthly_occurrence,
          publish_time: collection.publish_time,
        }
      })

      res.status(constants.STATUS_CODE.SUCESS.OK).send({
        message: 'Collections status retrieved successfully',
        success: true,
        data: {
          totalCollections: collections.length,
          collectionsWithStatus,
          currentTime,
          // Add circuit breaker status for monitoring
          circuitBreakerStatus: {
            peiqApi: peiqApiCircuitBreaker.getStatus(),
            directus: directusCircuitBreaker.getStatus()
          }
        },
      })
    } catch (error) {
      logger.error('Failed to get collections status', { error })
      
      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error instanceof Error ? error : new Error(String(error)),
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
        }),
      )
    }
  }

  /**
   * Get history of generated articles for a collection
   * GET /v1/recycling/collections/:collectionId/history
   */
  public static async getCollectionHistory(req: Request, res: Response) {
    const collectionId = requestUtil.getParameter(req, 'collectionId', undefined)

    if (!collectionId) {
      const missingParamError = new Error('Collection ID is required')
      logger.error(missingParamError)
      
      res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(
        new RmaError({
          error: missingParamError,
          statusCode: constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST,
        }),
      )
      return
    }

    try {
      const limit = requestUtil.getParameter(req, 'limit', 100)
      const bypassCache = requestUtil.getParameter(req, 'bypassCache', false)
      
      const history = await DirectusService.getGeneratedArticlesByCollection(
        collectionId, 
        { limit, bypassCache }
      )

      res.status(constants.STATUS_CODE.SUCESS.OK).send({
        message: 'Collection history retrieved successfully',
        success: true,
        data: {
          collectionId,
          totalGenerated: history.length,
          generatedArticles: history,
        },
      })
    } catch (error) {
      logger.error('Failed to get collection history', { error, collectionId })
      
      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error instanceof Error ? error : new Error(String(error)),
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
        }),
      )
    }
  }
}