import type { CollectionResult } from '../services/recyclingService.js'

export interface ErrorSummary {
  totalErrors: number
  errorsByType: Record<string, number>
  errorsByCollection: Record<string, string[]>
  retryableErrors: number
  criticalErrors: string[]
  recommendations: string[]
}

/**
 * Aggregates and analyzes errors from collection processing results
 */
export class ErrorAggregator {
  /**
   * Create a comprehensive error summary from collection results
   */
  static createErrorSummary(results: CollectionResult[]): ErrorSummary {
    const summary: ErrorSummary = {
      totalErrors: 0,
      errorsByType: {},
      errorsByCollection: {},
      retryableErrors: 0,
      criticalErrors: [],
      recommendations: [],
    }

    for (const result of results) {
      if (result.errors.length === 0) continue

      summary.totalErrors += result.errors.length
      summary.errorsByCollection[result.collectionId] = result.errors

      for (const error of result.errors) {
        this.analyzeError(error, summary)
      }
    }

    summary.recommendations = this.generateRecommendations(summary)

    return summary
  }

  /**
   * Analyze individual error and update summary
   */
  private static analyzeError(error: string, summary: ErrorSummary): void {
    // Extract error type from brackets, e.g. "[VALIDATION_ERROR]" -> "VALIDATION_ERROR"
    const errorTypeMatch = error.match(/\[(.*?)\]/)
    const errorType = errorTypeMatch ? errorTypeMatch[1] : 'UNKNOWN_ERROR'

    summary.errorsByType[errorType] = (summary.errorsByType[errorType] || 0) + 1

    if (error.includes('[RETRYABLE]')) {
      summary.retryableErrors++
    }

    if (
      error.includes('CATASTROPHIC') ||
      error.includes('DIRECTUS_ERROR') ||
      error.includes('AUTH_ERROR')
    ) {
      summary.criticalErrors.push(error)
    }
  }

  /**
   * Generate actionable recommendations based on error patterns
   */
  private static generateRecommendations(summary: ErrorSummary): string[] {
    const recommendations: string[] = []

    if (summary.errorsByType.EXTERNAL_API_ERROR > 3) {
      recommendations.push(
        'High number of PEIQ API errors detected. Consider:',
        '- Checking API rate limits',
        '- Implementing request throttling',
        '- Verifying API credentials',
      )
    }

    if (summary.errorsByType.NETWORK_ERROR > 2) {
      recommendations.push(
        'Network connectivity issues detected. Consider:',
        '- Checking network configuration',
        '- Verifying firewall rules',
        '- Testing DNS resolution',
      )
    }

    if (summary.errorsByType.VALIDATION_ERROR > 5) {
      recommendations.push(
        'Multiple validation errors found. Consider:',
        '- Reviewing collection configurations in Directus',
        '- Checking required fields are populated',
        '- Validating region and category assignments',
      )
    }

    if (summary.retryableErrors > summary.totalErrors * 0.5) {
      recommendations.push(
        'Many errors are retryable. Consider:',
        '- Implementing automatic retry mechanism',
        '- Scheduling manual re-execution',
        '- Increasing timeout thresholds',
      )
    }

    if (summary.criticalErrors.length > 0) {
      recommendations.push(
        'CRITICAL: System-level errors detected requiring immediate attention:',
        '- Check Directus connectivity and authentication',
        '- Verify service configuration',
        '- Review error logs for root cause',
      )
    }

    return recommendations
  }

  /**
   * Format error summary for logging or API response
   */
  static formatErrorSummary(summary: ErrorSummary): string {
    const lines: string[] = [
      '=== ERROR SUMMARY ===',
      `Total Errors: ${summary.totalErrors}`,
      `Retryable Errors: ${summary.retryableErrors}`,
      '',
      'Errors by Type:',
    ]

    for (const [type, count] of Object.entries(summary.errorsByType)) {
      lines.push(`  ${type}: ${count}`)
    }

    if (summary.criticalErrors.length > 0) {
      lines.push('', 'Critical Errors:')
      summary.criticalErrors.forEach((error) => {
        lines.push(`  - ${error}`)
      })
    }

    if (summary.recommendations.length > 0) {
      lines.push('', 'Recommendations:')
      summary.recommendations.forEach((rec) => {
        lines.push(`  - ${rec}`)
      })
    }

    return lines.join('\n')
  }
}
