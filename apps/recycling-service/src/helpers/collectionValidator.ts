import { logger } from '@regionalmedienaustria/microservice-utils'

import { TimeValidator } from '../utils/timeValidator.js'

import type { AutoContentCollection } from '../services/directusService.js'

/**
 * Validation rules for collection data before processing
 * Centralizes all collection validation logic
 */
export class CollectionValidator {
  
  static validateCollection(collection: AutoContentCollection): { isValid: boolean; error?: string } {
    const validationRules = [
      () => this.validateBasicFields(collection),
      () => this.validateStatus(collection),
      () => this.validateRegions(collection),
      () => this.validateCategories(collection),
      () => this.validateTimeInPast(collection),
    ]

    for (const rule of validationRules) {
      const error = rule()
      if (error) {
        return { isValid: false, error }
      }
    }

    return { isValid: true }
  }

  
  private static validateBasicFields(collection: AutoContentCollection): string | null {
    if (!collection) {
      return 'Collection is null or undefined'
    }
    
    if (!collection.id) {
      return 'Collection has no ID'
    }

    return null
  }

  
  private static validateStatus(collection: AutoContentCollection): string | null {
    if (!collection.status || collection.status !== 'published') {
      return `Collection status must be 'published', found: ${collection.status}`
    }

    return null
  }

  
  private static validateRegions(collection: AutoContentCollection): string | null {
    if (!collection.region || collection.region.length === 0) {
      return 'Collection has no regions specified'
    }

    return null
  }

  
  private static validateCategories(collection: AutoContentCollection): string | null {
    if (!collection.category || collection.category.length === 0) {
      return 'Collection has no categories specified'
    }

    return null
  }

  
  private static validateTimeInPast(collection: AutoContentCollection): string | null {
    const result = TimeValidator.validateTimeInPast(collection.time_in_past)
    
    if (!result.isValid) {
      return result.error || `Invalid time_in_past: ${collection.time_in_past}`
    }

    return null
  }

  
  static validateContentType(collection: AutoContentCollection): { isValid: boolean; warning?: string } {
    const supportedContentTypes = ['gallery_collection', 'article_collection']
    const contentType = collection.content_type?.toLowerCase()
    
    if (contentType && !supportedContentTypes.includes(contentType)) {
      const warning = `Unsupported content_type: ${collection.content_type}. Supported types: ${supportedContentTypes.join(', ')}`
      
      logger.warn('Unsupported content_type, proceeding with default article_collection type', {
        collectionId: collection.id,
        unsupportedType: collection.content_type,
        supportedTypes: supportedContentTypes
      })
      
      return { isValid: false, warning }
    }

    return { isValid: true }
  }

  
  static validateMinimumArticles(
    foundArticles: number, 
    collection: AutoContentCollection, 
    threshold: number
  ): { isValid: boolean; shouldProceed: boolean; warning?: string } {
    if (foundArticles < threshold) {
      const warning = `Not enough articles found for collection. Found: ${foundArticles}, minimum: ${threshold}`
      
      logger.info('Not enough articles found for collection', { 
        collectionId: collection.id,
        found: foundArticles,
        minimum: threshold 
      })
      
      // Per acceptance criteria: proceed with what we have
      logger.info('Proceeding with available articles as per acceptance criteria')
      
      return { 
        isValid: false, 
        shouldProceed: true, 
        warning 
      }
    }

    return { isValid: true, shouldProceed: true }
  }

  
  static validateHasArticles(foundArticles: number): { hasArticles: boolean; reason?: string } {
    if (foundArticles === 0) {
      return { 
        hasArticles: false, 
        reason: 'No top articles found for collection' 
      }
    }

    return { hasArticles: true }
  }
}